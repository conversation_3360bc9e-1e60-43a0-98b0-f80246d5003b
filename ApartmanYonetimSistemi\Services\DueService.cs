using Microsoft.EntityFrameworkCore;
using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public class DueService
    {
        private readonly ApplicationDbContext _context;

        public DueService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<Due>> GetAllDuesAsync()
        {
            return await _context.Dues
                .Include(d => d.User)
                .OrderByDescending(d => d.DueDate)
                .ToListAsync();
        }

        public async Task<List<Due>> GetDuesByApartmentAsync(string apartmentNumber, string block)
        {
            return await _context.Dues
                .Include(d => d.User)
                .Where(d => d.ApartmentNumber == apartmentNumber && d.Block == block)
                .OrderByDescending(d => d.DueDate)
                .ToListAsync();
        }

        public async Task<Due?> GetDueByIdAsync(int id)
        {
            return await _context.Dues
                .Include(d => d.User)
                .FirstOrDefaultAsync(d => d.Id == id);
        }

        public async Task<Due> CreateDueAsync(Due due)
        {
            _context.Dues.Add(due);
            await _context.SaveChangesAsync();
            return due;
        }

        public async Task<Due?> UpdateDueAsync(int id, Due updatedDue)
        {
            var due = await _context.Dues.FindAsync(id);
            if (due == null)
                return null;

            due.Amount = updatedDue.Amount;
            due.Description = updatedDue.Description;
            due.IsPaid = updatedDue.IsPaid;
            due.PaidDate = updatedDue.PaidDate;
            due.PaymentMethod = updatedDue.PaymentMethod;
            due.TransactionId = updatedDue.TransactionId;
            due.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return due;
        }

        public async Task<bool> DeleteDueAsync(int id)
        {
            var due = await _context.Dues.FindAsync(id);
            if (due == null)
                return false;

            _context.Dues.Remove(due);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<List<Due>> GetUnpaidDuesAsync()
        {
            return await _context.Dues
                .Include(d => d.User)
                .Where(d => !d.IsPaid)
                .OrderBy(d => d.DueDate)
                .ToListAsync();
        }

        public async Task<decimal> GetTotalUnpaidAmountAsync(string apartmentNumber, string block)
        {
            return await _context.Dues
                .Where(d => d.ApartmentNumber == apartmentNumber && 
                           d.Block == block && 
                           !d.IsPaid)
                .SumAsync(d => d.Amount);
        }
    }
} 