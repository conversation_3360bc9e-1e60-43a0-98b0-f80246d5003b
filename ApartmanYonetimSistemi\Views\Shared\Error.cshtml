@model ErrorViewModel
@{
    ViewData["Title"] = "Hata";
}

<div class="row justify-content-center">
    <div class="col-md-6 text-center">
        <div class="card shadow-sm">
            <div class="card-body p-5">
                <i class="bi bi-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                <h2 class="mt-4 mb-3">Bir Hata Oluştu</h2>
                <p class="text-muted mb-4">
                    İşleminiz sırasında beklenmeyen bir hata oluştu. Lütfen daha sonra tekrar deneyiniz.
                </p>
                @if (Model.ShowRequestId)
                {
                    <p class="text-muted small mb-4">
                        Hata ID: <code>@Model.RequestId</code>
                    </p>
                }
                <a asp-controller="Home" asp-action="Index" class="btn btn-primary">
                    <i class="bi bi-house"></i> Ana Sayfaya Dön
                </a>
            </div>
        </div>
    </div>
</div> 