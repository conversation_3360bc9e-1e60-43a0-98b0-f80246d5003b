# Apartman Yönetim <PERSON> - Mevcut Durum Analizi

## <PERSON><PERSON> Ba<PERSON>ş
- **.NET 8** tabanlı ASP.NET Core MVC web uygulaması
- Entity Framework Core ile veritabanı yönetimi
- Identity framework ile kullanıcı yönetimi
- Clean Architecture ve Repository Pattern kullanımı

## Mevcut Özellikler

### 1. Kullanıcı Yönetimi
- Kullanıcı kaydı ve girişi (`AccountController.cs`, `AuthController.cs`)
- Rol tabanlı yetkilendirme
- Güvenli şifre politikaları (minimum 8 karakter, rakam, kü<PERSON><PERSON>k/b<PERSON><PERSON><PERSON><PERSON> harf, alfanümerik olmayan karakter)
- Cookie tabanlı kimlik doğrulama

### 2. Aidat Yönetimi
- Aidat oluşturma ve listeleme (`DuesController.cs`, `DueService.cs`)
- Ödenmemiş aidatları görüntüleme
- Aidat ödeme takibi

### 3. <PERSON>ider Yönetimi
- Gider kategorileri tan<PERSON> (`ExpenseCategoriesController.cs`)
- Gider kayıtları oluşturma ve listeleme (`ExpensesController.cs`, `ExpenseService.cs`)
- Kategoriye göre gider listeleme
- Toplam gider hesaplama

### 4. Temel Görünümler
- Giriş/Kayıt sayfaları
- Dashboard
- Aidat ve gider listeleme sayfaları

### 5. Proje Yapısı
- Controllers/ - MVC Controller'ları
- Models/ - Veri modelleri
- Services/ - İş mantığı katmanı
- Views/ - Razor görünümleri
- Data/ - Veritabanı işlemleri
- wwwroot/ - Statik dosyalar (CSS, JS)