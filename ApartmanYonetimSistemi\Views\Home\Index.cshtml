@{
    ViewData["Title"] = "Ana Sayfa";
}

<!-- Modern Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center min-vh-100 py-5">
            <div class="col-lg-6">
                <div class="hero-content">
                    <h1 class="hero-title">
                        Modern
                        <span class="text-gradient">Apartman Yönetimi</span>
                    </h1>
                    <p class="hero-subtitle">
                        Apartmanınızı dijital çağa taşıyın. Aidat takibi, gider yönetimi ve daha fazlası için
                        modern, kullanıc<PERSON> dostu çözümümüzü keşfedin.
                    </p>
                    <div class="hero-actions">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <a asp-controller="Home" asp-action="Dashboard" class="btn btn-primary btn-lg me-3">
                                <i class="bi bi-grid-1x2-fill me-2"></i>Dashboard'a Git
                            </a>
                            <a href="#features" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-info-circle me-2"></i>Özellikler
                            </a>
                        }
                        else
                        {
                            <a asp-controller="Account" asp-action="Register" class="btn btn-primary btn-lg me-3">
                                <i class="bi bi-person-plus-fill me-2"></i>Hemen Başla
                            </a>
                            <a asp-controller="Account" asp-action="Login" class="btn btn-outline-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Giriş Yap
                            </a>
                        }
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="hero-image">
                    <div class="floating-card">
                        <div class="card shadow-lg">
                            <div class="card-body p-4">
                                <div class="d-flex align-items-center mb-3">
                                    <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                        <i class="bi bi-buildings-fill text-primary fs-4"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">Apartman Yönetimi</h6>
                                        <small class="text-muted">Gerçek Zamanlı Takip</small>
                                    </div>
                                </div>
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="text-center p-2 bg-success bg-opacity-10 rounded">
                                            <div class="fw-bold text-success">₺8,750</div>
                                            <small class="text-muted">Ödenen</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center p-2 bg-warning bg-opacity-10 rounded">
                                            <div class="fw-bold text-warning">₺3,700</div>
                                            <small class="text-muted">Bekleyen</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<section id="features" class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5">
                <h2 class="section-title">
                    <span class="text-gradient">Güçlü Özellikler</span>
                </h2>
                <p class="section-subtitle">
                    Apartman yönetiminizi kolaylaştıran modern araçlar ve özellikler
                </p>
            </div>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon bg-primary">
                        <i class="bi bi-wallet-fill"></i>
                    </div>
                    <h5 class="feature-title">Aidat Yönetimi</h5>
                    <p class="feature-description">
                        Aylık aidat takibi, ödeme durumu kontrolü ve otomatik hatırlatmalar ile
                        aidat yönetiminizi kolaylaştırın.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon bg-success">
                        <i class="bi bi-receipt-cutoff"></i>
                    </div>
                    <h5 class="feature-title">Gider Takibi</h5>
                    <p class="feature-description">
                        Tüm apartman giderlerini kategorize edin, fatura yükleyin ve
                        detaylı raporlar oluşturun.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon bg-info">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <h5 class="feature-title">Raporlama</h5>
                    <p class="feature-description">
                        Gelir-gider raporları, aidat durumu analizi ve finansal
                        özetler ile apartmanınızı analiz edin.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon bg-warning">
                        <i class="bi bi-megaphone-fill"></i>
                    </div>
                    <h5 class="feature-title">Duyuru Sistemi</h5>
                    <p class="feature-description">
                        Apartman sakinlerine duyuru gönderin, önemli bilgileri
                        paylaşın ve iletişimi güçlendirin.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon bg-danger">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h5 class="feature-title">Güvenli Erişim</h5>
                    <p class="feature-description">
                        Rol tabanlı yetkilendirme sistemi ile verilerinizi güvende tutun
                        ve erişimi kontrol edin.
                    </p>
                </div>
            </div>

            <div class="col-lg-4 col-md-6">
                <div class="feature-card">
                    <div class="feature-icon bg-secondary">
                        <i class="bi bi-phone"></i>
                    </div>
                    <h5 class="feature-title">Mobil Uyumlu</h5>
                    <p class="feature-description">
                        Responsive tasarım ile her cihazdan erişim sağlayın.
                        Telefon, tablet ve bilgisayardan kullanın.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        // Parallax effect for hero section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-section');
            const speed = scrolled * 0.5;

            if (parallax) {
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Animate feature cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all feature cards
        document.querySelectorAll('.feature-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            card.style.transition = 'all 0.6s ease-out';
            observer.observe(card);
        });
    </script>
}

