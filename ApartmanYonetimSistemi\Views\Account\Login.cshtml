@model LoginViewModel

@{
    ViewData["Title"] = "Giriş Yap";
}

<div class="row justify-content-center align-items-center min-vh-100">
    <div class="col-md-6 col-lg-5 col-xl-4">
        <div class="auth-card-modern">
            <!-- Modern Header -->
            <div class="auth-header">
                <div class="auth-logo">
                    <div class="logo-icon">
                        <i class="bi bi-buildings-fill"></i>
                    </div>
                    <h2 class="logo-text">Apartman Yönetimi</h2>
                </div>
                <p class="auth-subtitle">Hesabınıza giriş yapın</p>
            </div>

            <!-- Form Content -->
            <div class="auth-body">
                <form asp-controller="Account" asp-action="Login" method="post" class="modern-form">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger modern-alert" role="alert"></div>

                    <div class="form-group-modern">
                        <div class="input-wrapper">
                            <i class="bi bi-envelope-fill input-icon"></i>
                            <input asp-for="Email" class="form-control-modern" placeholder="E-posta adresinizi girin" />
                            <label asp-for="Email" class="form-label-modern">E-posta</label>
                        </div>
                        <span asp-validation-for="Email" class="validation-message"></span>
                    </div>

                    <div class="form-group-modern">
                        <div class="input-wrapper">
                            <i class="bi bi-lock-fill input-icon"></i>
                            <input asp-for="Password" class="form-control-modern" placeholder="Şifrenizi girin" type="password" />
                            <label asp-for="Password" class="form-label-modern">Şifre</label>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="bi bi-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                        <span asp-validation-for="Password" class="validation-message"></span>
                    </div>

                    <div class="form-check-modern">
                        <input asp-for="RememberMe" class="form-check-input-modern" id="rememberMeCheck" />
                        <label asp-for="RememberMe" class="form-check-label-modern" for="rememberMeCheck">
                            <span class="checkmark"></span>
                            Beni Hatırla
                        </label>
                    </div>

                    <button type="submit" class="btn-modern btn-primary-modern">
                        <span class="btn-text">Giriş Yap</span>
                        <i class="bi bi-arrow-right btn-icon"></i>
                    </button>
                </form>

                <div class="auth-footer">
                    <p class="auth-link-text">
                        Hesabınız yok mu?
                        <a asp-controller="Account" asp-action="Register" class="auth-link">Hemen Kayıt Olun</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.querySelector('input[name="Password"]');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // Modern form interactions
        document.querySelectorAll('.form-control-modern').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            // Check if input has value on page load
            if (input.value) {
                input.parentElement.classList.add('focused');
            }
        });

        // Form submission animation
        document.querySelector('.modern-form').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('.btn-modern');
            submitBtn.classList.add('loading');
            submitBtn.querySelector('.btn-text').textContent = 'Giriş yapılıyor...';
            submitBtn.querySelector('.btn-icon').className = 'bi bi-arrow-clockwise spin';
        });
    </script>
}

