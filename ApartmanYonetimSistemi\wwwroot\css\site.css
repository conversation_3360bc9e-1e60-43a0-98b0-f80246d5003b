/* Modern Apartman Yö<PERSON>im Sistemi CSS - v2.2 */
/* CSS YÜKLENDI - TEST BAŞARILI! */
/* Bu dosya yüklenirse body arka planı gradient olacak */

/* CSS Test Marker - Bu yorum görünüyorsa CSS dosyası başarıyla yüklendi */

/* CSS Test - Bu stil aktifse sayfanın sol üst köşesinde kırmızı bir nokta görünecek */
body::before {
  content: "CSS YÜKLENDI ✓";
  position: fixed;
  top: 10px;
  left: 10px;
  background: #ef4444;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  font-family: monospace;
}

/* CSS Test - Bu yorum görünüyorsa CSS yüklendi */
html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
  font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif !important;
  color: #1e293b !important;
  line-height: 1.6 !important;
  font-weight: 400 !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  font-size: 0.95rem !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.25) !important;
}

.btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4) !important;
  background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%) !important;
}

/* Modern CSS Variables for Consistent Design */
:root {
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #8b5cf6;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  --light-color: #f8fafc;
  --dark-color: #1e293b;

  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;

  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;

  --border-color: #e2e8f0;
  --border-light: #f1f5f9;

  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

html {
  font-size: 14px;
  scroll-behavior: smooth;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

body {
  font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: var(--text-primary);
  line-height: 1.6;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container, .container-fluid {
    max-width: 1400px;
}

/* Modern Header & Navbar */
.navbar {
  padding: 1rem 0 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border-bottom: 1px solid rgba(226, 232, 240, 0.8) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
}

.navbar.scrolled {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  background: rgba(255, 255, 255, 0.98) !important;
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color) !important;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all var(--transition-fast);
}

.navbar-brand:hover {
  transform: translateY(-1px);
  color: var(--primary-dark) !important;
}

.navbar-brand i {
  font-size: 1.75rem;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar .nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  position: relative;
  margin: 0 0.25rem;
}

.navbar .nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  border-radius: var(--radius-lg);
  opacity: 0;
  transition: opacity var(--transition-fast);
  z-index: -1;
}

.navbar .nav-link:hover,
.navbar .nav-link.active {
  color: white !important;
  transform: translateY(-2px);
}

.navbar .nav-link:hover::before,
.navbar .nav-link.active::before {
  opacity: 1;
}

.navbar .dropdown-menu {
  border: none;
  box-shadow: var(--shadow-xl);
  border-radius: var(--radius-lg);
  padding: 0.5rem;
  background: var(--bg-primary);
  backdrop-filter: blur(10px);
  margin-top: 0.5rem;
}

.navbar .dropdown-item {
  padding: 0.75rem 1rem;
  font-weight: 500;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  color: var(--text-secondary);
}

.navbar .dropdown-item:hover {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  transform: translateX(4px);
}

.navbar-toggler {
  border: none;
  padding: 0.5rem;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.navbar-toggler:focus {
  box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
}

.navbar-toggler-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='%236366f1' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  transition: all var(--transition-fast);
}


/* Modern Main Content Area */
main {
  flex-grow: 1;
  padding: 2rem 0 3rem;
  position: relative;
}

/* Modern Card Styles */
.card {
  border: none !important;
  border-radius: 16px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  margin-bottom: 2rem !important;
  background: #ffffff !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  overflow: hidden !important;
  position: relative !important;
}

.card:hover {
  transform: translateY(-4px) !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.card:hover::before {
  opacity: 1;
}

.card-header {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem 2rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  position: relative;
}

.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6 {
  margin: 0;
  color: var(--text-primary);
}

.card-body {
  padding: 2rem;
  background: var(--bg-primary);
}

.card-footer {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-color);
  padding: 1.5rem 2rem;
}

/* Modern Form Elements */
.form-label {
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-control, .form-select {
  border-radius: 12px !important;
  border: 2px solid #e2e8f0 !important;
  padding: 1rem 1.25rem !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  background: #ffffff !important;
  font-size: 1rem !important;
  font-weight: 400 !important;
  color: #1e293b !important;
}

.form-control:focus, .form-select:focus {
  border-color: #6366f1 !important;
  box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.1) !important;
  transform: translateY(-2px) !important;
  background: #ffffff !important;
  outline: none !important;
}

.form-control::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label {
  color: var(--primary-color);
  font-weight: 600;
}

.input-group-text {
  background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  border: 2px solid var(--border-color);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-weight: 500;
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
}

/* Modern Buttons */
.btn {
  padding: 0.875rem 1.75rem;
  font-weight: 600;
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
  letter-spacing: 0.5px;
  border: 2px solid transparent;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  font-size: 0.875rem;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left var(--transition-slow);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  border-color: var(--primary-color);
  color: white;
}
.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
  border-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color), #475569);
  border-color: var(--secondary-color);
  color: white;
}
.btn-secondary:hover {
  background: linear-gradient(135deg, #475569, var(--secondary-color));
  border-color: #475569;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), #059669);
  border-color: var(--success-color);
  color: white;
}
.btn-success:hover {
  background: linear-gradient(135deg, #059669, var(--success-color));
  border-color: #059669;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
  border-color: var(--danger-color);
  color: white;
}
.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, var(--danger-color));
  border-color: #dc2626;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
  border-color: var(--warning-color);
  color: white;
}
.btn-warning:hover {
  background: linear-gradient(135deg, #d97706, var(--warning-color));
  border-color: #d97706;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-info {
  background: linear-gradient(135deg, var(--info-color), #0891b2);
  border-color: var(--info-color);
  color: white;
}
.btn-info:hover {
  background: linear-gradient(135deg, #0891b2, var(--info-color));
  border-color: #0891b2;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-light {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  color: var(--text-primary);
}
.btn-light:hover {
  background: var(--bg-tertiary);
  border-color: var(--text-muted);
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  color: var(--text-primary);
}

.btn-dark {
  background: linear-gradient(135deg, var(--dark-color), #0f172a);
  border-color: var(--dark-color);
  color: white;
}
.btn-dark:hover {
  background: linear-gradient(135deg, #0f172a, var(--dark-color));
  border-color: #0f172a;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
  color: white;
}

.btn-outline-primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}
.btn-outline-primary:hover {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: var(--shadow-lg);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
}

.btn-lg {
  padding: 1rem 2rem;
  font-size: 1rem;
}

/* Tables */
.table {
  border-radius: 0.5rem; /* Rounded corners for the table wrapper if any */
  overflow: hidden; /* Ensures child elements adhere to border-radius */
  margin-bottom: 1rem;
  background-color: #fff; /* Ensure table has white background */
  box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.04); /* Subtle shadow for tables */
}

.table thead th {
  background-color: #f8f9fa; /* Light header for tables */
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #495057;
  padding: 0.9rem 1rem;
  vertical-align: middle;
}

.table tbody td {
  padding: 0.9rem 1rem;
  vertical-align: middle;
  border-top: 1px solid #e9ecef; /* Lighter row separators */
}

.table-hover tbody tr:hover {
  background-color: rgba(13, 110, 253, 0.03); /* Very subtle hover for table rows */
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.02); /* Lighter stripe */
}

.table-bordered {
    border: 1px solid #dee2e6;
}
.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

/* Alerts */
.alert {
  border: none;
  border-radius: 0.5rem; /* Consistent rounding */
  padding: 1rem 1.25rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,0.08); /* Soft shadow for alerts */
}

.alert-heading {
    font-weight: 600;
}

.alert-dismissible .btn-close {
    padding: 1.15rem 1.25rem; /* Better touch target for close button */
}

/* Footer */
.footer {
  padding: 1.5rem 0;
  font-size: 0.9rem;
  background-color: #fff; /* White footer for cleaner look */
}

/* Utility Classes */
.shadow-sm { box-shadow: 0 .125rem .25rem rgba(0,0,0,.075)!important; }
.shadow { box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important; }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important; }

.fw-medium { font-weight: 500 !important; }
.fw-semibold { font-weight: 600 !important; }

.text-muted-light { color: #868e96 !important; } /* Lighter muted text */

/* Authentication Page Specific Styles */
.auth-page-background {
    background-color: #e9ecef; /* A slightly different background for auth pages */
    display: flex;
    align-items: center; /* Vertically center content */
    justify-content: center; /* Horizontally center content */
    padding-top: 0; /* Remove default body padding */
    padding-bottom: 0;
}

.auth-container {
    width: 100%;
    max-width: none; /* Allow auth card to define its own max-width */
    padding-left: 15px; /* Standard container padding */
    padding-right: 15px;
}

.auth-main {
    padding-top: 0; /* Remove main padding for auth pages */
    padding-bottom: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.auth-card {
    width: 100%; /* Card takes full width of its column */
    /* max-width is set in Login.cshtml and Register.cshtml column classes e.g. col-lg-4 */
    margin-top: 2rem; /* Add some top margin */
    margin-bottom: 2rem; /* Add some bottom margin */
    border: none; /* Remove border if any from general card style */
}

.auth-card .card-header {
    text-align: center;
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: none; /* Remove border if header is distinct */
}

.auth-card .card-body {
    /* Padding is already set in Login.cshtml and Register.cshtml */
}

.auth-card .navbar-brand { /* If you use brand in auth pages */
    display: block;
    text-align: center;
    margin-bottom: 1rem;
    font-size: 1.75rem; /* Larger brand for auth pages */
    color: #fff; /* Assuming a dark card header */
}

/* Ensure form floating labels are visible on auth pages if background changes */
.auth-page-background .form-floating > .form-control {
    background-color: #fff;
}

.auth-page-background .form-floating > label {
    color: #495057; /* Ensure label color is standard */
}


/* Modern Dashboard Stat Cards */
.stat-card {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
  border-radius: var(--radius-xl);
  padding: 2rem;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
  transition: all var(--transition-normal);
  border: none;
  box-shadow: var(--shadow-lg);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.stat-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card .stat-title {
  font-size: 0.875rem;
  font-weight: 600;
  opacity: 0.9;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stat-card .stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-card .stat-icon {
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 4rem;
  opacity: 0.15;
  transition: all var(--transition-normal);
}

.stat-card:hover .stat-icon {
  opacity: 0.25;
  transform: translateY(-50%) scale(1.1);
}

/* Stat Card Variants */
.stat-card.bg-primary {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}
.stat-card.bg-success {
  background: linear-gradient(135deg, var(--success-color), #059669);
}
.stat-card.bg-warning {
  background: linear-gradient(135deg, var(--warning-color), #d97706);
}
.stat-card.bg-danger {
  background: linear-gradient(135deg, var(--danger-color), #dc2626);
}
.stat-card.bg-info {
  background: linear-gradient(135deg, var(--info-color), #0891b2);
}
.stat-card.bg-secondary {
  background: linear-gradient(135deg, var(--secondary-color), #475569);
}


/* Custom Scrollbar (Optional, for a more modern feel) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Modern Animations */
.animate-in {
  animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.notification-modern {
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Table Styles */
.table th[data-sort] {
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color var(--transition-fast);
}

.table th[data-sort]:hover {
  background-color: var(--bg-tertiary) !important;
}

.table th[data-sort]::after {
  content: '↕';
  position: absolute;
  right: 0.5rem;
  opacity: 0.5;
  font-size: 0.8rem;
}

.table th.sort-asc::after {
  content: '↑';
  opacity: 1;
  color: var(--primary-color);
}

.table th.sort-desc::after {
  content: '↓';
  opacity: 1;
  color: var(--primary-color);
}

/* User Avatar Styles */
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

/* Enhanced Navbar Transitions */
.navbar {
  transition: all var(--transition-normal), transform var(--transition-fast);
}

.navbar.scrolled {
  backdrop-filter: blur(15px);
}

/* Loading States */
.btn.loading {
  pointer-events: none;
  opacity: 0.8;
}

.btn.loading .spin {
  animation: spin 1s linear infinite;
}

/* Form Validation Enhancements */
.form-control.is-valid {
  border-color: var(--success-color);
  box-shadow: 0 0 0 0.25rem rgba(16, 185, 129, 0.1);
}

.form-control.is-invalid {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 0.25rem rgba(239, 68, 68, 0.1);
}

.invalid-feedback {
  display: block;
  color: var(--danger-color);
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 991.98px) {
  .navbar-nav {
    margin-top: 0.5rem;
  }
  .navbar .nav-link {
    padding: 0.8rem 1rem;
  }

  .stat-card {
    margin-bottom: 1.5rem;
  }

  .hero-title {
    font-size: 2.5rem !important;
  }

  .hero-subtitle {
    font-size: 1.1rem !important;
  }
}

@media (max-width: 767.98px) {
  body {
    font-size: 0.95rem;
  }

  .card-header {
    font-size: 1.05rem;
    padding: 1rem 1.5rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }

  .stat-card .stat-value {
    font-size: 2rem;
  }

  .stat-card .stat-icon {
    font-size: 3rem;
  }

  .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .hero-actions .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }

  .feature-card {
    padding: 1.5rem;
  }

  .navbar {
    padding: 0.5rem 0;
  }

  /* Fixed navbar spacing for mobile */
  .navbar + div {
    height: 70px !important;
  }
}

@media (max-width: 575.98px) {
  .stat-card {
    text-align: center;
  }

  .stat-card .stat-icon {
    position: static;
    transform: none;
    margin-bottom: 1rem;
    font-size: 2.5rem;
  }

  .table-responsive {
    font-size: 0.875rem;
  }

  .btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* Hero Section Styles */
.hero-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  color: white;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.text-gradient {
  background: linear-gradient(45deg, #ffd89b 0%, #19547b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  line-height: 1.6;
}

.hero-actions {
  margin-top: 2rem;
}

.floating-card {
  position: relative;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: #6c757d;
  margin-bottom: 0;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  transition: all 0.3s ease;
  height: 100%;
  border: 1px solid #e2e8f0;
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1);
}

.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: white;
}

.feature-title {
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1e293b;
}

.feature-description {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 0;
}

/* Modern Auth Styles */
.auth-card-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 2rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
}

.auth-card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6366f1, #8b5cf6, #06b6d4);
}

.auth-header {
  padding: 3rem 3rem 1rem;
  text-align: center;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
}

.auth-logo {
  margin-bottom: 1rem;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  border-radius: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
  font-size: 2rem;
  color: white;
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.logo-text {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.auth-subtitle {
  color: #64748b;
  font-size: 1rem;
  margin: 0;
}

.auth-body {
  padding: 2rem 3rem 3rem;
}

.form-group-modern {
  margin-bottom: 1.5rem;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 1rem;
  color: #94a3b8;
  font-size: 1.1rem;
  z-index: 2;
  transition: color 0.3s ease;
}

.form-control-modern {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  font-size: 1rem;
  background: #f8fafc;
  transition: all 0.3s ease;
  outline: none;
}

.form-control-modern:focus {
  border-color: #6366f1;
  background: white;
  box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.1);
  transform: translateY(-2px);
}

.input-wrapper.focused .input-icon {
  color: #6366f1;
}

.form-label-modern {
  position: absolute;
  left: 3rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 1rem;
  pointer-events: none;
  transition: all 0.3s ease;
  background: transparent;
  padding: 0 0.5rem;
}

.input-wrapper.focused .form-label-modern,
.form-control-modern:focus + .form-label-modern {
  top: 0;
  left: 2.5rem;
  font-size: 0.75rem;
  color: #6366f1;
  background: white;
}

.password-toggle {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.password-toggle:hover {
  color: #6366f1;
  background: rgba(99, 102, 241, 0.1);
}

.validation-message {
  color: #ef4444;
  font-size: 0.875rem;
  margin-top: 0.5rem;
  display: block;
}

.form-check-modern {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
}

.form-check-input-modern {
  display: none;
}

.form-check-label-modern {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.95rem;
  color: #64748b;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 0.375rem;
  margin-right: 0.75rem;
  position: relative;
  transition: all 0.3s ease;
}

.form-check-input-modern:checked + .form-check-label-modern .checkmark {
  background: #6366f1;
  border-color: #6366f1;
}

.form-check-input-modern:checked + .form-check-label-modern .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
}

.btn-modern {
  width: 100%;
  padding: 1rem 2rem;
  border: none;
  border-radius: 1rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.btn-primary-modern {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 10px 25px rgba(99, 102, 241, 0.3);
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(99, 102, 241, 0.4);
}

.btn-modern.loading {
  pointer-events: none;
  opacity: 0.8;
}

.btn-icon {
  transition: transform 0.3s ease;
}

.btn-modern:hover .btn-icon {
  transform: translateX(4px);
}

.auth-footer {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.auth-link-text {
  color: #64748b;
  margin: 0;
}

.auth-link {
  color: #6366f1;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.auth-link:hover {
  color: #4f46e5;
}

.modern-alert {
  border: none;
  border-radius: 1rem;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-left: 4px solid #ef4444;
  margin-bottom: 1.5rem;
}

/* Auth Page Specific Styles */
.auth-page-background {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  position: relative;
}

.auth-page-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.auth-container {
  position: relative;
  z-index: 1;
  padding: 0;
}

.auth-main {
  padding: 0;
  min-height: 100vh;
  display: flex;
  align-items: center;
}