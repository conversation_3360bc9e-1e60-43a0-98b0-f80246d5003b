// Modern Apartman Yö<PERSON> - Enhanced JavaScript

// Global utilities and modern enhancements
document.addEventListener('DOMContentLoaded', function() {
    initializeModernFeatures();
    initializeAnimations();
    initializeTooltips();
    initializeFormEnhancements();
});

// Initialize modern UI features
function initializeModernFeatures() {
    // Enhanced navbar scroll effect
    let lastScrollTop = 0;
    const navbar = document.getElementById('mainNavbar');

    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        if (navbar) {
            if (scrollTop > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }

            // Hide/show navbar on scroll
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }
        }

        lastScrollTop = scrollTop;
    });

    // Add loading states to buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', function(e) {
            if (this.type === 'submit' && !this.classList.contains('btn-outline')) {
                addLoadingState(this);
            }
        });
    });

    // Auto-hide alerts with animation
    setTimeout(() => {
        document.querySelectorAll('.alert').forEach(alert => {
            if (alert.classList.contains('alert-success')) {
                fadeOutElement(alert);
            }
        });
    }, 5000);
}

// Initialize scroll animations with Intersection Observer
function initializeAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');

                // Special animation for stat cards
                if (entry.target.classList.contains('stat-card')) {
                    animateStatCard(entry.target);
                }
            }
        });
    }, observerOptions);

    // Observe cards and other elements
    document.querySelectorAll('.card, .stat-card, .feature-card').forEach(element => {
        observer.observe(element);
    });
}

// Initialize enhanced tooltips
function initializeTooltips() {
    // Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Bootstrap popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

// Enhanced form features
function initializeFormEnhancements() {
    // Enhanced form validation
    document.querySelectorAll('.form-control').forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });

        input.addEventListener('input', function() {
            clearValidationState(this);
        });

        // Add focus effects
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });

    // Form submission handling
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.classList.contains('loading')) {
                addLoadingState(submitBtn);
            }
        });
    });
}

// Animate stat cards with number counting
function animateStatCard(card) {
    const valueElement = card.querySelector('.stat-value');
    if (valueElement) {
        const finalValue = valueElement.textContent;
        const numericValue = parseFloat(finalValue.replace(/[^\d.-]/g, ''));

        if (!isNaN(numericValue)) {
            let currentValue = 0;
            const increment = numericValue / 30;
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= numericValue) {
                    currentValue = numericValue;
                    clearInterval(timer);
                }

                const formattedValue = finalValue.includes('₺')
                    ? `₺${currentValue.toLocaleString('tr-TR', {minimumFractionDigits: 2, maximumFractionDigits: 2})}`
                    : Math.round(currentValue).toLocaleString('tr-TR');

                valueElement.textContent = formattedValue;
            }, 50);
        }
    }
}

// Add loading state to buttons
function addLoadingState(button) {
    const originalText = button.innerHTML;
    const loadingText = button.dataset.loading || 'Yükleniyor...';

    button.innerHTML = `<i class="bi bi-arrow-clockwise spin me-2"></i>${loadingText}`;
    button.disabled = true;
    button.classList.add('loading');

    // Store original text for restoration
    button.dataset.originalText = originalText;
}

// Remove loading state from buttons
function removeLoadingState(button) {
    if (button.dataset.originalText) {
        button.innerHTML = button.dataset.originalText;
        button.disabled = false;
        button.classList.remove('loading');
        delete button.dataset.originalText;
    }
}

// Enhanced field validation
function validateField(field) {
    const value = field.value.trim();
    const fieldType = field.type;
    const isRequired = field.hasAttribute('required');

    clearValidationState(field);

    if (isRequired && !value) {
        showFieldError(field, 'Bu alan zorunludur.');
        return false;
    }

    if (fieldType === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'Geçerli bir e-posta adresi girin.');
            return false;
        }
    }

    if (fieldType === 'password' && value) {
        if (value.length < 6) {
            showFieldError(field, 'Şifre en az 6 karakter olmalıdır.');
            return false;
        }
    }

    showFieldSuccess(field);
    return true;
}

// Show field error state
function showFieldError(field, message) {
    field.classList.add('is-invalid');
    field.classList.remove('is-valid');

    let feedback = field.parentNode.querySelector('.invalid-feedback');
    if (!feedback) {
        feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        field.parentNode.appendChild(feedback);
    }
    feedback.textContent = message;
}

// Show field success state
function showFieldSuccess(field) {
    field.classList.add('is-valid');
    field.classList.remove('is-invalid');

    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
}

// Clear validation state
function clearValidationState(field) {
    field.classList.remove('is-valid', 'is-invalid');
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback) {
        feedback.remove();
    }
}

// Smooth fade out animation
function fadeOutElement(element) {
    element.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
    element.style.opacity = '0';
    element.style.transform = 'translateY(-20px)';

    setTimeout(() => {
        if (element.parentNode) {
            element.remove();
        }
    }, 500);
}

// Modern notification system
function showNotification(message, type = 'info', duration = 4000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed notification-modern`;
    notification.style.cssText = `
        top: 100px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        max-width: 400px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        border: none;
        border-radius: 1rem;
        backdrop-filter: blur(10px);
    `;

    const icons = {
        success: 'bi-check-circle-fill',
        danger: 'bi-exclamation-triangle-fill',
        warning: 'bi-exclamation-circle-fill',
        info: 'bi-info-circle-fill'
    };

    notification.innerHTML = `
        <i class="bi ${icons[type] || icons.info} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove
    setTimeout(() => {
        if (notification.parentNode) {
            fadeOutElement(notification);
        }
    }, duration);

    return notification;
}

// Enhanced table sorting
function initializeTableSorting() {
    document.querySelectorAll('th[data-sort]').forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            const table = this.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            const index = Array.from(this.parentNode.children).indexOf(this);
            const direction = this.dataset.direction === 'asc' ? -1 : 1;

            rows.sort((a, b) => {
                const aValue = a.children[index].textContent.trim();
                const bValue = b.children[index].textContent.trim();
                return direction * aValue.localeCompare(bValue, undefined, {numeric: true});
            });

            rows.forEach(row => tbody.appendChild(row));
            this.dataset.direction = direction === 1 ? 'asc' : 'desc';

            // Update sort indicators
            table.querySelectorAll('th[data-sort]').forEach(th => {
                th.classList.remove('sort-asc', 'sort-desc');
            });
            this.classList.add(direction === 1 ? 'sort-asc' : 'sort-desc');
        });
    });
}

// Initialize table sorting when DOM is ready
document.addEventListener('DOMContentLoaded', initializeTableSorting);

// Export functions for global use
window.ModernUI = {
    showNotification,
    addLoadingState,
    removeLoadingState,
    validateField,
    fadeOutElement
};