using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;

namespace ApartmanYonetimSistemi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class DuesController : ControllerBase
    {
        private readonly DueService _dueService;

        public DuesController(DueService dueService)
        {
            _dueService = dueService;
        }

        [HttpGet]
        public async Task<ActionResult<List<Due>>> GetAllDues()
        {
            var dues = await _dueService.GetAllDuesAsync();
            return Ok(dues);
        }

        [HttpGet("apartment/{apartmentNumber}/{block}")]
        public async Task<ActionResult<List<Due>>> GetDuesByApartment(string apartmentNumber, string block)
        {
            var dues = await _dueService.GetDuesByApartmentAsync(apartmentNumber, block);
            return Ok(dues);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Due>> GetDueById(int id)
        {
            var due = await _dueService.GetDueByIdAsync(id);
            if (due == null)
                return NotFound();

            return Ok(due);
        }

        [HttpPost]
        public async Task<ActionResult<Due>> CreateDue([FromBody] Due due)
        {
            var createdDue = await _dueService.CreateDueAsync(due);
            return CreatedAtAction(nameof(GetDueById), new { id = createdDue.Id }, createdDue);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<Due>> UpdateDue(int id, [FromBody] Due updatedDue)
        {
            var due = await _dueService.UpdateDueAsync(id, updatedDue);
            if (due == null)
                return NotFound();

            return Ok(due);
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteDue(int id)
        {
            var result = await _dueService.DeleteDueAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }

        [HttpGet("unpaid")]
        public async Task<ActionResult<List<Due>>> GetUnpaidDues()
        {
            var dues = await _dueService.GetUnpaidDuesAsync();
            return Ok(dues);
        }

        [HttpGet("unpaid/total/{apartmentNumber}/{block}")]
        public async Task<ActionResult<decimal>> GetTotalUnpaidAmount(string apartmentNumber, string block)
        {
            var total = await _dueService.GetTotalUnpaidAmountAsync(apartmentNumber, block);
            return Ok(new { total });
        }
    }
} 