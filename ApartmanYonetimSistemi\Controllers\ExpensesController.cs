using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;

namespace ApartmanYonetimSistemi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class ExpensesController : ControllerBase
    {
        private readonly ExpenseService _expenseService;

        public ExpensesController(ExpenseService expenseService)
        {
            _expenseService = expenseService;
        }

        [HttpGet]
        public async Task<ActionResult<List<Expense>>> GetAllExpenses()
        {
            var expenses = await _expenseService.GetAllExpensesAsync();
            return Ok(expenses);
        }

        [HttpGet("category/{categoryId}")]
        public async Task<ActionResult<List<Expense>>> GetExpensesByCategory(int categoryId)
        {
            var expenses = await _expenseService.GetExpensesByCategoryAsync(categoryId);
            return Ok(expenses);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<Expense>> GetExpenseById(int id)
        {
            var expense = await _expenseService.GetExpenseByIdAsync(id);
            if (expense == null)
                return NotFound();

            return Ok(expense);
        }

        [HttpPost]
        public async Task<ActionResult<Expense>> CreateExpense([FromBody] Expense expense)
        {
            var createdExpense = await _expenseService.CreateExpenseAsync(expense);
            return CreatedAtAction(nameof(GetExpenseById), new { id = createdExpense.Id }, createdExpense);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<Expense>> UpdateExpense(int id, [FromBody] Expense updatedExpense)
        {
            var expense = await _expenseService.UpdateExpenseAsync(id, updatedExpense);
            if (expense == null)
                return NotFound();

            return Ok(expense);
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteExpense(int id)
        {
            var result = await _expenseService.DeleteExpenseAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }

        [HttpGet("total")]
        public async Task<ActionResult<decimal>> GetTotalExpenses(
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            var total = await _expenseService.GetTotalExpensesByDateRangeAsync(startDate, endDate);
            return Ok(new { total });
        }

        [HttpGet("total/category/{categoryId}")]
        public async Task<ActionResult<decimal>> GetTotalExpensesByCategory(
            int categoryId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            var total = await _expenseService.GetTotalExpensesByCategoryAsync(categoryId, startDate, endDate);
            return Ok(new { total });
        }
    }
} 