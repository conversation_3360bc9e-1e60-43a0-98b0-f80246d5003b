{"Version": 1, "Hash": "o39kdXWuGucGSPBmOXB9cQI4WdB5JT/wFplojpN8FAw=", "Source": "ApartmanYonetimSistemi", "BasePath": "_content/ApartmanYonetimSistemi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "ApartmanYonetimSistemi\\wwwroot", "Source": "ApartmanYonetimSistemi", "ContentRoot": "C:\\AI\\Apartman\\ApartmanYonetimSistemi\\wwwroot\\", "BasePath": "_content/ApartmanYonetimSistemi", "Pattern": "**"}], "Assets": [{"Identity": "C:\\AI\\Apartman\\ApartmanYonetimSistemi\\wwwroot\\css\\site.css", "SourceId": "ApartmanYonetimSistemi", "SourceType": "Discovered", "ContentRoot": "C:\\AI\\Apartman\\ApartmanYonetimSistemi\\wwwroot\\", "BasePath": "_content/ApartmanYonetimSistemi", "RelativePath": "css/site.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css"}, {"Identity": "C:\\AI\\Apartman\\ApartmanYonetimSistemi\\wwwroot\\js\\site.js", "SourceId": "ApartmanYonetimSistemi", "SourceType": "Discovered", "ContentRoot": "C:\\AI\\Apartman\\ApartmanYonetimSistemi\\wwwroot\\", "BasePath": "_content/ApartmanYonetimSistemi", "RelativePath": "js/site.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js"}]}