using System.ComponentModel.DataAnnotations;

namespace ApartmanYonetimSistemi.Models
{
    public class Due
    {
        public int Id { get; set; }

        [Required]
        public string ApartmentNumber { get; set; } = string.Empty;

        [Required]
        public string Block { get; set; } = string.Empty;

        [Required]
        public DateTime DueDate { get; set; }

        [Required]
        public decimal Amount { get; set; }

        public string? Description { get; set; }

        public bool IsPaid { get; set; } = false;

        public DateTime? PaidDate { get; set; }

        public string? PaymentMethod { get; set; }

        public string? TransactionId { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }

        // Navigation property
        public ApplicationUser? User { get; set; }
        public string? UserId { get; set; }
    }
} 