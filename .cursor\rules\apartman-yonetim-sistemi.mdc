---
description: 
globs: 
alwaysApply: false
---
# Apartman Yönetim Sistemi Proje Planı

## 1. <PERSON><PERSON>
- Proje <PERSON>: Apartman Yönetim <PERSON>
- Teknoloji Stack: .NET Core 8.0, Entity Framework Core, SQL Server
- <PERSON><PERSON><PERSON>: Clean Architecture, Repository Pattern
- Frontend: ASP.NET Core MVC + Bootstrap 5

## 2. Temel Özellikler

### 2.1 Kullanıcı Yönetimi
- Rol tabanlı yetkilendirme (Admin, Yönetici, Daire Sakinleri)
- Kullanıcı kayıt ve giriş sistemi
- Profil yönetimi
- Şifre sıfırlama

### 2.2 Daire ve Sakin Yönetimi
- <PERSON>re bilgileri (numarası, kat, blok, m2)
- Daire sakinleri bilgileri
- <PERSON>ç plaka bilgileri
- Evcil hayvan kayıtları

### 2.3 Aidat Yönetimi
- Aylık aidat belirleme
- Aidat ödeme takibi
- Borç/alacak raporları
- Otomatik aidat hesaplama
- Online ödeme entegrasyonu

### 2.4 Gider Yönetimi
- Gider kategorileri
- Fatura girişi ve takibi
- Gider raporları
- Bütçe planlama

### 2.5 Duyuru ve İletişim
- Duyuru yayınlama
- E-posta bildirimleri
- SMS bildirimleri
- Şikayet/öneri sistemi

### 2.6 Rezervasyon Sistemi
- Toplantı salonu rezervasyonu
- Spor salonu rezervasyonu
- Misafir park yeri rezervasyonu

## 3. Veritabanı Tasarımı

### 3.1 Ana Tablolar
- Users (Kullanıcılar)
- Apartments (Daireler)
- Residents (Sakinler)
- Dues (Aidatlar)
- Expenses (Giderler)
- Announcements (Duyurular)
- Reservations (Rezervasyonlar)
- Payments (Ödemeler)

## 4. Geliştirme Aşamaları

### Faz 1: Temel Altyapı (2 Hafta)
- Proje kurulumu ve mimari yapılandırma
- Veritabanı tasarımı
- Temel CRUD işlemleri
- Kullanıcı yönetimi

### Faz 2: Ana Özellikler (3 Hafta)
- Daire ve sakin yönetimi
- Aidat yönetimi
- Gider yönetimi
- Temel raporlama

### Faz 3: İleri Özellikler (2 Hafta)
- Duyuru sistemi
- Rezervasyon sistemi
- Bildirim sistemi
- Gelişmiş raporlama

### Faz 4: Test ve Optimizasyon (1 Hafta)
- Birim testleri
- Entegrasyon testleri
- Performans optimizasyonu
- Güvenlik testleri

## 5. Teknik Gereksinimler

### 5.1 Backend
- .NET Core 8.0
- Entity Framework Core
- SQL Server
- AutoMapper
- FluentValidation
- JWT Authentication
- Serilog

### 5.2 Frontend
- ASP.NET Core MVC
- Bootstrap 5
- jQuery
- DataTables
- Chart.js

### 5.3 DevOps
- Git versiyon kontrolü
- Azure DevOps CI/CD
- Docker containerization
- Azure App Service deployment

## 6. Güvenlik Önlemleri
- HTTPS zorunluluğu
- SQL Injection koruması
- XSS koruması
- CSRF koruması
- Veri şifreleme
- Rol tabanlı yetkilendirme

## 7. Performans Hedefleri
- Sayfa yüklenme süresi < 2 saniye
- API yanıt süresi < 500ms
- Eşzamanlı kullanıcı desteği: 100+
- Veritabanı sorgu optimizasyonu

## 8. Bakım ve Destek
- Hata izleme ve loglama
- Yedekleme stratejisi
- Performans izleme
- Kullanıcı geri bildirim sistemi
- Düzenli güvenlik güncellemeleri

