# Proje <PERSON>zi: ApartmanYonetimSistemi

## Genel Ba<PERSON>ı<PERSON>:

*   **Framework:** .NET 8 ([`ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:4`](ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:4))
*   **Proje Tipi:** ASP.NET Core Web Uygulaması (MVC) ([`ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:1`](ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:1), [`ApartmanYonetimSistemi/Program.cs:10`](ApartmanYonetimSistemi/Program.cs:10))
*   **Veritabanı:** Entity Framework Core ile SQL Server kullanılıyor ([`ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:16`](ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:16), [`ApartmanYonetimSistemi/Program.cs:13-14`](ApartmanYonetimSistemi/Program.cs:13-14)). Veritabanı bağlantı ayarları muhtemelen [`appsettings.json`](ApartmanYonetimSistemi/appsettings.json) dosyasındaki "DefaultConnection" üzerinden yapılandırılıyor.
*   **Kimlik Doğrulama ve Yetkilendirme:** ASP.NET Core Identity kullanılıyor ([`ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:10`](ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:10), [`ApartmanYonetimSistemi/Program.cs:17-26`](ApartmanYonetimSistemi/Program.cs:17-26)).
    *   Şifre politikaları belirlenmiş (rakam, küçük harf, büyük harf, alfanümerik olmayan karakter zorunluluğu ve minimum 8 karakter uzunluk) ([`ApartmanYonetimSistemi/Program.cs:19-23`](ApartmanYonetimSistemi/Program.cs:19-23)).
    *   Cookie tabanlı kimlik doğrulama kullanılıyor ve oturum ayarları yapılandırılmış (Login, Logout, AccessDenied yolları, oturum süresi vb.) ([`ApartmanYonetimSistemi/Program.cs:34-41`](ApartmanYonetimSistemi/Program.cs:34-41)).
*   **API Dokümantasyonu:** Swashbuckle.AspNetCore (Swagger) entegrasyonu mevcut ([`ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:17`](ApartmanYonetimSistemi/ApartmanYonetimSistemi.csproj:17)).
*   **Yapılandırma:** [`appsettings.json`](ApartmanYonetimSistemi/appsettings.json) ve geliştirme ortamı için [`appsettings.Development.json`](ApartmanYonetimSistemi/appsettings.Development.json) dosyaları kullanılıyor.
*   **Başlangıç Yolu (Default Route):** Uygulama varsayılan olarak `Account/Login` adresine yönlendiriliyor ([`ApartmanYonetimSistemi/Program.cs:61-63`](ApartmanYonetimSistemi/Program.cs:61-63)).

## Proje Yapısı ve Bileşenleri:

*   **Modeller ([`ApartmanYonetimSistemi/Models/`](ApartmanYonetimSistemi/Models/)):**
    *   [`ApplicationUser.cs`](ApartmanYonetimSistemi/Models/ApplicationUser.cs:5-14): ASP.NET Core Identity kullanıcısını temsil eder.
    *   [`Due.cs`](ApartmanYonetimSistemi/Models/Due.cs:5-38): Aidat bilgilerini tutar.
    *   [`Expense.cs`](ApartmanYonetimSistemi/Models/Expense.cs:5-42): Gider bilgilerini tutar.
    *   [`ExpenseCategory.cs`](ApartmanYonetimSistemi/Models/ExpenseCategory.cs:5-23): Gider kategorilerini tanımlar.
    *   [`ErrorViewModel.cs`](ApartmanYonetimSistemi/Models/ErrorViewModel.cs:3-8): Hata sayfaları için model.
*   **Controller'lar ([`ApartmanYonetimSistemi/Controllers/`](ApartmanYonetimSistemi/Controllers/)):**
    *   [`AccountController.cs`](ApartmanYonetimSistemi/Controllers/AccountController.cs:9-110): Kullanıcı girişi, kaydı, çıkışı ve erişim reddi gibi hesap işlemlerini yönetir.
    *   [`AuthController.cs`](ApartmanYonetimSistemi/Controllers/AuthController.cs:9-83): API tabanlı kimlik doğrulama işlemleri (login, register, logout) için endpoint'ler sunar.
    *   [`DuesController.cs`](ApartmanYonetimSistemi/Controllers/DuesController.cs:8-84): Aidatlarla ilgili CRUD (Oluşturma, Okuma, Güncelleme, Silme) işlemlerini ve ödenmemiş aidatları listeleme gibi fonksiyonları içerir.
    *   [`ExpenseCategoriesController.cs`](ApartmanYonetimSistemi/Controllers/ExpenseCategoriesController.cs:8-63): Gider kategorileri için CRUD işlemlerini yönetir.
    *   [`ExpensesController.cs`](ApartmanYonetimSistemi/Controllers/ExpensesController.cs:8-89): Giderlerle ilgili CRUD işlemlerini, kategoriye göre gider listeleme ve toplam gider hesaplama gibi fonksiyonları içerir.
    *   [`HomeController.cs`](ApartmanYonetimSistemi/Controllers/HomeController.cs:8-34): Ana sayfa (Index), Dashboard ve hata sayfası gibi genel sayfaları yönetir.
*   **Servisler ([`ApartmanYonetimSistemi/Services/`](ApartmanYonetimSistemi/Services/)):**
    *   [`UserService.cs`](ApartmanYonetimSistemi/Services/UserService.cs:6-64): Kullanıcı oluşturma, giriş yapma, çıkış yapma, şifre işlemleri gibi kullanıcı yönetimiyle ilgili iş mantığını içerir.
    *   [`DueService.cs`](ApartmanYonetimSistemi/Services/DueService.cs:7-93): Aidatlarla ilgili iş mantığını (tüm aidatları, daireye göre aidatları, ödenmemiş aidatları getirme, aidat oluşturma, güncelleme, silme vb.) barındırır.
    *   [`ExpenseService.cs`](ApartmanYonetimSistemi/Services/ExpenseService.cs:7-147): Giderler ve gider kategorileriyle ilgili iş mantığını (kategori ve gider CRUD işlemleri, kategoriye göre giderleri, belirli tarih aralığındaki toplam giderleri getirme vb.) içerir.
*   **Veri Katmanı ([`ApartmanYonetimSistemi/Data/`](ApartmanYonetimSistemi/Data/)):**
    *   [`ApplicationDbContext.cs`](ApartmanYonetimSistemi/Data/ApplicationDbContext.cs): Entity Framework Core DbContext sınıfını miras alır ve veritabanı tablolarıyla etkileşim kurar.
*   **Görünümler (Views) ([`ApartmanYonetimSistemi/Views/`](ApartmanYonetimSistemi/Views/)):** Kullanıcı arayüzünü oluşturan Razor (.cshtml) dosyalarını içerir.
    *   Paylaşılan layout ([`_Layout.cshtml`](ApartmanYonetimSistemi/Views/Shared/_Layout.cshtml:0)), importlar ([`_ViewImports.cshtml`](ApartmanYonetimSistemi/Views/_ViewImports.cshtml:0)) ve başlangıç ayarları ([`_ViewStart.cshtml`](ApartmanYonetimSistemi/Views/Shared/_ViewStart.cshtml:0)) mevcuttur.
    *   Hesap ([`Account`](ApartmanYonetimSistemi/Views/Account/)), Ana Sayfa ([`Home`](ApartmanYonetimSistemi/Views/Home/)) gibi controller'lara karşılık gelen klasörlerde ilgili view'lar bulunur.
*   **Statik Dosyalar ([`ApartmanYonetimSistemi/wwwroot/`](ApartmanYonetimSistemi/wwwroot/)):** CSS ([`site.css`](ApartmanYonetimSistemi/wwwroot/css/site.css:0)), JavaScript ([`site.js`](ApartmanYonetimSistemi/wwwroot/js/site.js:0)) gibi statik dosyalar burada yer alır.

## Özetle:

Projeniz, apartman yönetimiyle ilgili temel işlevleri (kullanıcı yönetimi, aidat takibi, gider takibi) yerine getiren, iyi yapılandırılmış bir web uygulamasıdır. ASP.NET Core'un sunduğu modern özellikleri (Dependency Injection, Identity, Entity Framework Core) etkin bir şekilde kullanmaktadır. API endpoint'leri sayesinde gelecekte mobil veya farklı istemci uygulamalarıyla entegrasyon potansiyeli de bulunmaktadır.