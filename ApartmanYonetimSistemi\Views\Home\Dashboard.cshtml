@{
    ViewData["Title"] = "Dashboard";
}

<!-- Modern Dashboard Header -->
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 mb-1 fw-bold text-primary">Dashboard</h1>
                    <p class="text-muted mb-0">Apartman yönetim sistemi genel görünümü</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshDashboard()">
                        <i class="bi bi-arrow-clockwise me-1"></i>Yenile
                    </button>
                    <button class="btn btn-primary btn-sm" onclick="showComingSoon('Rapor İndir')">
                        <i class="bi bi-download me-1"></i><PERSON><PERSON> İndir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Stat Cards -->
    <div class="row g-4 mb-5">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card bg-primary">
                <div class="stat-title">Toplam Aidat</div>
                <div class="stat-value">₺12,450.00</div>
                <div class="stat-icon">
                    <i class="bi bi-cash-stack"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card bg-success">
                <div class="stat-title">Ödenen Aidat</div>
                <div class="stat-value">₺8,750.00</div>
                <div class="stat-icon">
                    <i class="bi bi-check-circle-fill"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card bg-danger">
                <div class="stat-title">Ödenmemiş Aidat</div>
                <div class="stat-value">₺3,700.00</div>
                <div class="stat-icon">
                    <i class="bi bi-exclamation-triangle-fill"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card bg-info">
                <div class="stat-title">Toplam Gider</div>
                <div class="stat-value">₺5,230.00</div>
                <div class="stat-icon">
                    <i class="bi bi-receipt-cutoff"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Content Cards -->
    <div class="row g-4">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-clock-history me-2 text-primary"></i>
                        Son Aidat Ödemeleri
                    </h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="showComingSoon('Filtrele')">
                            <i class="bi bi-funnel"></i>
                        </button>
                        <a href="#" onclick="showComingSoon('Yeni Aidat')" class="btn btn-primary btn-sm">
                            <i class="bi bi-plus-lg me-1"></i>Yeni Aidat
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0">Tarih</th>
                                    <th class="border-0">Daire</th>
                                    <th class="border-0">Tutar</th>
                                    <th class="border-0">Durum</th>
                                    <th class="border-0">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                                                <i class="bi bi-calendar3 text-primary"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold">15 Aralık 2024</div>
                                                <small class="text-muted">Aralık Aidatı</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark fw-semibold">A Blok - 12</span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-success">₺850.00</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <i class="bi bi-check-circle me-1"></i>Ödendi
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="showComingSoon('Görüntüle')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-secondary" onclick="showComingSoon('Düzenle')">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="bg-warning bg-opacity-10 rounded-circle p-2 me-3">
                                                <i class="bi bi-calendar3 text-warning"></i>
                                            </div>
                                            <div>
                                                <div class="fw-semibold">10 Aralık 2024</div>
                                                <small class="text-muted">Aralık Aidatı</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark fw-semibold">B Blok - 5</span>
                                    </td>
                                    <td>
                                        <span class="fw-bold text-danger">₺850.00</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">
                                            <i class="bi bi-clock me-1"></i>Bekliyor
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="showComingSoon('Görüntüle')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="showComingSoon('Öde')">
                                                <i class="bi bi-credit-card"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="bi bi-info-circle me-2"></i>
                                        Daha fazla kayıt için
                                        <a href="#" onclick="showComingSoon('Aidatlar')" class="text-decoration-none">Aidatlar</a>
                                        sayfasını ziyaret edin.
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="bi bi-receipt me-2 text-primary"></i>
                        Son Giderler
                    </h5>
                    <a href="#" onclick="showComingSoon('Yeni Gider')" class="btn btn-primary btn-sm">
                        <i class="bi bi-plus-lg me-1"></i>Yeni Gider
                    </a>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center p-3 border rounded-3 mb-3">
                        <div class="bg-danger bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-lightning-charge text-danger"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-semibold">Elektrik Faturası</div>
                            <small class="text-muted">12 Aralık 2024</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-danger">₺1,250.00</div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center p-3 border rounded-3 mb-3">
                        <div class="bg-info bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-droplet text-info"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-semibold">Su Faturası</div>
                            <small class="text-muted">10 Aralık 2024</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-info">₺680.00</div>
                        </div>
                    </div>

                    <div class="d-flex align-items-center p-3 border rounded-3 mb-3">
                        <div class="bg-success bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-tools text-success"></i>
                        </div>
                        <div class="flex-grow-1">
                            <div class="fw-semibold">Bakım Onarım</div>
                            <small class="text-muted">8 Aralık 2024</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold text-success">₺450.00</div>
                        </div>
                    </div>

                    <div class="text-center pt-3 border-top">
                        <a href="#" onclick="showComingSoon('Tüm Giderler')" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-arrow-right me-1"></i>Tüm Giderleri Görüntüle
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning-fill me-2 text-primary"></i>
                        Hızlı İşlemler
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="showComingSoon('Aidat Tahsil')">
                            <i class="bi bi-cash-coin me-2"></i>Aidat Tahsil Et
                        </button>
                        <button class="btn btn-outline-success" onclick="showComingSoon('Gider Ekle')">
                            <i class="bi bi-plus-circle me-2"></i>Gider Ekle
                        </button>
                        <button class="btn btn-outline-info" onclick="showComingSoon('Duyuru Yayınla')">
                            <i class="bi bi-megaphone me-2"></i>Duyuru Yayınla
                        </button>
                        <button class="btn btn-outline-warning" onclick="showComingSoon('Rapor Oluştur')">
                            <i class="bi bi-file-earmark-text me-2"></i>Rapor Oluştur
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Dashboard refresh function
        function refreshDashboard() {
            // Show loading state
            const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
            const originalText = refreshBtn.innerHTML;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1 spin"></i>Yenileniyor...';
            refreshBtn.disabled = true;

            // Simulate refresh (replace with actual API call)
            setTimeout(() => {
                refreshBtn.innerHTML = originalText;
                refreshBtn.disabled = false;

                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
                alert.style.cssText = 'top: 100px; right: 20px; z-index: 9999; min-width: 300px;';
                alert.innerHTML = `
                    <i class="bi bi-check-circle me-2"></i>
                    Dashboard başarıyla yenilendi!
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                document.body.appendChild(alert);

                // Auto remove after 3 seconds
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.remove();
                    }
                }, 3000);
            }, 1500);
        }

        // Spin animation is already defined in site.css
    </script>
}