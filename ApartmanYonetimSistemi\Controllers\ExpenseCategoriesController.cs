using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using ApartmanYonetimSistemi.Models;
using ApartmanYonetimSistemi.Services;

namespace ApartmanYonetimSistemi.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/[controller]")]
    public class ExpenseCategoriesController : ControllerBase
    {
        private readonly ExpenseService _expenseService;

        public ExpenseCategoriesController(ExpenseService expenseService)
        {
            _expenseService = expenseService;
        }

        [HttpGet]
        public async Task<ActionResult<List<ExpenseCategory>>> GetAllCategories()
        {
            var categories = await _expenseService.GetAllCategoriesAsync();
            return Ok(categories);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ExpenseCategory>> GetCategoryById(int id)
        {
            var category = await _expenseService.GetCategoryByIdAsync(id);
            if (category == null)
                return NotFound();

            return Ok(category);
        }

        [HttpPost]
        public async Task<ActionResult<ExpenseCategory>> CreateCategory([FromBody] ExpenseCategory category)
        {
            var createdCategory = await _expenseService.CreateCategoryAsync(category);
            return CreatedAtAction(nameof(GetCategoryById), new { id = createdCategory.Id }, createdCategory);
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<ExpenseCategory>> UpdateCategory(int id, [FromBody] ExpenseCategory updatedCategory)
        {
            var category = await _expenseService.UpdateCategoryAsync(id, updatedCategory);
            if (category == null)
                return NotFound();

            return Ok(category);
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteCategory(int id)
        {
            var result = await _expenseService.DeleteCategoryAsync(id);
            if (!result)
                return NotFound();

            return NoContent();
        }
    }
} 