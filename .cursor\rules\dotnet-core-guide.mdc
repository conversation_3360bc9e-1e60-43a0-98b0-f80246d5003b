---
description: 
globs: 
alwaysApply: false
---
# .NET Core Proje Yapısı Rehberi

## Proje Yapısı
- Program.cs: Uygulamanın ana giriş noktası
- Startup.cs: Uygulama yapılandırması ve servis kayıtları
- appsettings.json: Uygulama ayarları
- Properties/launchSettings.json: Debug ve çalıştırma ayarları

## Katmanlı Mimari
- Controllers/: API endpoint'leri
- Models/: Veri modelleri
- Services/: İş mantığı katmanı
- Data/: Veritabanı işlemleri
- DTOs/: Veri transfer nesneleri

## Önemli Klasörler
- wwwroot/: Statik dosyalar (CSS, JS, resimler)
- Views/: MVC görünümleri
- Areas/: Modüler yapılar için alanlar

## Best Practices
1. SOLID prensiplerini takip edin
2. Dependency Injection kullanın
3. Async/await pattern'ini uygulayın
4. Exception handling için global middleware kullanın
5. Logging için ILogger interface'ini kullanın

## NuGet Paketleri
- Microsoft.EntityFrameworkCore
- AutoMapper
- Swashbuckle.AspNetCore
- Serilog
- FluentValidation

## Test Projeleri
- xUnit test framework'ü kullanın
- Moq ile mock'lama yapın
- Integration testleri için TestServer kullanın

## Deployment
- Docker container'ları için Dockerfile kullanın
- CI/CD pipeline'ları için GitHub Actions veya Azure DevOps kullanın
- Environment-specific ayarlar için appsettings.{Environment}.json kullanın

