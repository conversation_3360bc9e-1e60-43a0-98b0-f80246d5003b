using Microsoft.EntityFrameworkCore;
using ApartmanYonetimSistemi.Data;
using ApartmanYonetimSistemi.Models;

namespace ApartmanYonetimSistemi.Services
{
    public class ExpenseService
    {
        private readonly ApplicationDbContext _context;

        public ExpenseService(ApplicationDbContext context)
        {
            _context = context;
        }

        // Kategori işlemleri
        public async Task<List<ExpenseCategory>> GetAllCategoriesAsync()
        {
            return await _context.ExpenseCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<ExpenseCategory?> GetCategoryByIdAsync(int id)
        {
            return await _context.ExpenseCategories.FindAsync(id);
        }

        public async Task<ExpenseCategory> CreateCategoryAsync(ExpenseCategory category)
        {
            _context.ExpenseCategories.Add(category);
            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<ExpenseCategory?> UpdateCategoryAsync(int id, ExpenseCategory updatedCategory)
        {
            var category = await _context.ExpenseCategories.FindAsync(id);
            if (category == null)
                return null;

            category.Name = updatedCategory.Name;
            category.Description = updatedCategory.Description;
            category.IsActive = updatedCategory.IsActive;
            category.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return category;
        }

        public async Task<bool> DeleteCategoryAsync(int id)
        {
            var category = await _context.ExpenseCategories.FindAsync(id);
            if (category == null)
                return false;

            category.IsActive = false;
            category.UpdatedAt = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }

        // Gider işlemleri
        public async Task<List<Expense>> GetAllExpensesAsync()
        {
            return await _context.Expenses
                .Include(e => e.Category)
                .Include(e => e.CreatedBy)
                .OrderByDescending(e => e.ExpenseDate)
                .ToListAsync();
        }

        public async Task<List<Expense>> GetExpensesByCategoryAsync(int categoryId)
        {
            return await _context.Expenses
                .Include(e => e.Category)
                .Include(e => e.CreatedBy)
                .Where(e => e.CategoryId == categoryId)
                .OrderByDescending(e => e.ExpenseDate)
                .ToListAsync();
        }

        public async Task<Expense?> GetExpenseByIdAsync(int id)
        {
            return await _context.Expenses
                .Include(e => e.Category)
                .Include(e => e.CreatedBy)
                .FirstOrDefaultAsync(e => e.Id == id);
        }

        public async Task<Expense> CreateExpenseAsync(Expense expense)
        {
            _context.Expenses.Add(expense);
            await _context.SaveChangesAsync();
            return expense;
        }

        public async Task<Expense?> UpdateExpenseAsync(int id, Expense updatedExpense)
        {
            var expense = await _context.Expenses.FindAsync(id);
            if (expense == null)
                return null;

            expense.CategoryId = updatedExpense.CategoryId;
            expense.Amount = updatedExpense.Amount;
            expense.ExpenseDate = updatedExpense.ExpenseDate;
            expense.Description = updatedExpense.Description;
            expense.ReceiptNumber = updatedExpense.ReceiptNumber;
            expense.ReceiptImageUrl = updatedExpense.ReceiptImageUrl;
            expense.IsPaid = updatedExpense.IsPaid;
            expense.PaidDate = updatedExpense.PaidDate;
            expense.PaymentMethod = updatedExpense.PaymentMethod;
            expense.TransactionId = updatedExpense.TransactionId;
            expense.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            return expense;
        }

        public async Task<bool> DeleteExpenseAsync(int id)
        {
            var expense = await _context.Expenses.FindAsync(id);
            if (expense == null)
                return false;

            _context.Expenses.Remove(expense);
            await _context.SaveChangesAsync();
            return true;
        }

        public async Task<decimal> GetTotalExpensesByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.Expenses
                .Where(e => e.ExpenseDate >= startDate && e.ExpenseDate <= endDate)
                .SumAsync(e => e.Amount);
        }

        public async Task<decimal> GetTotalExpensesByCategoryAsync(int categoryId, DateTime startDate, DateTime endDate)
        {
            return await _context.Expenses
                .Where(e => e.CategoryId == categoryId && 
                           e.ExpenseDate >= startDate && 
                           e.ExpenseDate <= endDate)
                .SumAsync(e => e.Amount);
        }
    }
} 