# Apartman Yönetim <PERSON>u proje, apartman yönetimini kolaylaştırmak için geliştirilmiş bir web uygulamasıdır. .NET Core MVC kullanılarak geliştirilmiştir.

## Geliştirilen Özellikler

### 1. Kullanıcı Yönetimi
- Kullanıcı kaydı ve girişi
- Kullanıcı profil yönetimi
- Rol tabanlı yetkilendirme
- Güvenli şifre politikaları

### 2. Aidat Yönetimi
- Aylık aidat oluşturma
- Aidat ödeme takibi
- Ödenmemiş aidatların listelenmesi
- Aidat ödeme geçmişi

### 3. Gider Yönetimi
- Gider kategorileri
- Gider kayıtları
- Makbuz yükleme
- Gider raporlama
- Kategori bazlı gider analizi

### 4. Veritabanı Yapısı
- Entity Framework Core ile veritabanı yönetimi
- Code First yaklaşımı
- İlişkisel veritabanı tasarımı
- Migration sistemi

### 5. Güvenlik
- Identity framework entegrasyonu
- JWT tabanlı kimlik doğrulama
- Şifre hashleme
- Güvenli oturum yönetimi

### 6. Kullanıcı Arayüzü
- Responsive tasarım
- Bootstrap framework
- Modern ve kullanıcı dostu arayüz
- Form validasyonları

## Teknik Detaylar

### Kullanılan Teknolojiler
- .NET Core 7.0
- Entity Framework Core
- SQL Server
- Bootstrap 5
- jQuery
- Identity Framework

### Proje Yapısı
```
ApartmanYonetimSistemi/
├── Controllers/
│   ├── AccountController.cs
│   ├── AuthController.cs
│   ├── DuesController.cs
│   ├── ExpensesController.cs
│   └── HomeController.cs
├── Models/
│   ├── ApplicationUser.cs
│   ├── Due.cs
│   ├── Expense.cs
│   └── ExpenseCategory.cs
├── Services/
│   ├── DueService.cs
│   └── ExpenseService.cs
├── ViewModels/
│   ├── LoginViewModel.cs
│   └── RegisterViewModel.cs
└── Views/
    ├── Account/
    ├── Dues/
    ├── Expenses/
    └── Home/
```

### API Endpoints

#### Kullanıcı İşlemleri
- POST /api/auth/register - Kullanıcı kaydı
- POST /api/auth/login - Kullanıcı girişi
- POST /api/auth/logout - Çıkış yapma

#### Aidat İşlemleri
- GET /api/dues - Tüm aidatları listele
- GET /api/dues/{id} - Belirli bir aidatı getir
- POST /api/dues - Yeni aidat oluştur
- PUT /api/dues/{id} - Aidat güncelle
- DELETE /api/dues/{id} - Aidat sil

#### Gider İşlemleri
- GET /api/expenses - Tüm giderleri listele
- GET /api/expenses/category/{categoryId} - Kategori bazlı giderleri listele
- POST /api/expenses - Yeni gider oluştur
- PUT /api/expenses/{id} - Gider güncelle
- DELETE /api/expenses/{id} - Gider sil

## Kurulum

1. Projeyi klonlayın
```bash
git clone https://github.com/kullanici/apartman-yonetim-sistemi.git
```

2. Veritabanını oluşturun
```bash
dotnet ef database update
```

3. Projeyi çalıştırın
```bash
dotnet run
```

## Geliştirme Süreci

1. Temel proje yapısı oluşturuldu
2. Veritabanı modelleri tasarlandı
3. Identity framework entegrasyonu yapıldı
4. Kullanıcı yönetimi geliştirildi
5. Aidat yönetimi eklendi
6. Gider yönetimi eklendi
7. MVC yapısına geçiş yapıldı
8. Kullanıcı arayüzü geliştirildi

## Gelecek Özellikler

1. Duyuru sistemi
2. Rezervasyon sistemi
3. Raporlama sistemi
4. Dosya yükleme sistemi
5. E-posta bildirimleri
6. Mobil uygulama desteği

## Katkıda Bulunma

1. Bu depoyu fork edin
2. Yeni bir branch oluşturun (`git checkout -b feature/yeniOzellik`)
3. Değişikliklerinizi commit edin (`git commit -am 'Yeni özellik eklendi'`)
4. Branch'inizi push edin (`git push origin feature/yeniOzellik`)
5. Pull Request oluşturun

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için [LICENSE](LICENSE) dosyasına bakın. 